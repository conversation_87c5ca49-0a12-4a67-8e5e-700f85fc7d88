name: Bug Report
description: File a bug report.
title: "[Bug]: "
labels: ["bug"]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to fill out this bug report!
  - type: textarea
    id: what-happened
    attributes:
      label: What happened?
      description: Also tell us, what did you expect to happen?
      placeholder: Tell us what you see!
      value: |
        <!-- Please describe what happened here. -->
    validations:
      required: true
  - type: textarea
    id: expected
    attributes:
      label: Expected behavior
      description: Please describe what you expected to happen.
      placeholder: Tell us what you expected to happen!
      value: |
        <!-- Please describe what you expected to happen here. -->
    validations:
      required: true
  - type: textarea
    id: steps
    attributes:
      label: Steps to reproduce
      description: Please describe the steps to reproduce the issue.
      placeholder: Tell us what you expected to happen!
      value: |
        <!-- Please describe the steps to reproduce the issue here. -->
    validations:
      required: true
  - type: textarea
    id: additional-context
    attributes:
      label: Additional context
      description: Please provide any additional context that may help us understand the issue.
      placeholder: Tell us what you think!
      value: |
        <!-- Please provide any additional context that may help us understand the issue here. -->
    validations:
      required: false
  - type: dropdown
    id: os
    attributes:
      label: Operating System
      description: What operating system are you using?
      options:
        - Windows
        - MacOS
        - Linux
      default: 0
    validations:
      required: true
  - type: textarea
    id: logs
    attributes:
      label: Relevant log output
      description: Please copy and paste any relevant log output. This will be automatically formatted into code, so no need for backticks.
      render: shell
  - type: checkboxes
    id: checklist
    attributes:
      label: Checklist
      description: Please check all that apply.
      options:
        - label: I have checked that the issue has not already been reported
        - label: I have included reproducible steps
        - label: I am using the latest version of the project (running off of main)
        - label: I have included any screenshots that may help us understand the issue
    validations:
      required: true
