# This workflow will install Python dependencies, run tests and lint with a variety of Python versions
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-python
name: Python package
on:
  push:
    branches: ["main", 'stable/*']
    paths:
      - '**/*.py'
      - '**/*.pyi'
      - 'requirements.txt'
      - 'noxfile.py'
  pull_request:
    branches: ["main", 'stable/*']
    paths:
      - '**/*.py'
      - '**/*.pyi'
      - 'requirements.txt'
      - 'noxfile.py'
jobs:
  build:
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        python-version: ["3.12"]
        os: [ubuntu-latest, windows-latest, macOS-latest]
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v3
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install nox
        run: |
          python -m pip install --upgrade pip
          python -m pip install nox
      - name: Run format with nox
        run: nox -s format
        env:
          PYTHONPATH: ${{ github.workspace }}
      - name: Run lint with nox
        run: nox -s lint
        env:
          PYTHONPATH: ${{ github.workspace }}
      - name: Run tests with nox
        run: nox -s tests
        env:
          PYTHONPATH: ${{ github.workspace }}
