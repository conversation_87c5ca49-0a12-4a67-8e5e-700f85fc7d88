name: Document Formatter
on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  yaml-format:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Format YAML files
        uses: docker://ghcr.io/google/yamlfmt:latest
        with:
          args: .
      - name: Commit changes
        run: |
          git config --global user.name 'github-actions'
          git config --global user.email '<EMAIL>'
          git add .
          git commit -m 'Formatted YAML files'
      - name: Check for changes
        run: |
          git diff --exit-code