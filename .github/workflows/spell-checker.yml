name: Spell Checker
on:
  workflow_dispatch:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  spell-check:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
      - name: Check Spelling
        uses: check-spelling/check-spelling@v0.0.24
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
        continue-on-error: true
        